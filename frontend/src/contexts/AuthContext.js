/**
 * Authentication Context - Manages user authentication state
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { notification } from 'antd';

const AuthContext = createContext();

// Auto-login service URL (for Google OAuth)
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3000';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasValidPackage, setHasValidPackage] = useState(false);

  // Check authentication status on app load and set up periodic checks
  useEffect(() => {
    checkAuthStatus();

    // Set up periodic session check every 5 minutes
    const sessionCheckInterval = setInterval(() => {
      if (isAuthenticated) {
        console.log('🔍 [AUTH] Performing periodic session check...');
        checkAuthStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(sessionCheckInterval);
    };
  }, [isAuthenticated]);

  const checkAuthStatus = async () => {
    try {
      setLoading(true);

      // Check if user data exists in localStorage
      const storedUser = localStorage.getItem('user');
      const storedAuth = localStorage.getItem('isAuthenticated');

      if (storedUser && storedAuth === 'true') {
        console.log('🔍 [AUTH] Verifying stored session with server...');

        // Verify with backend API (which will proxy to auto-login service)
        const response = await fetch('/api/auth/verify-cookies', {
          method: 'GET',
          credentials: 'include', // Include HTTP-only cookies
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ [AUTH] Session verified successfully');
          setUser(data.user);
          setIsAuthenticated(true);

          // Check package access
          await checkPackageAccess(data.user.id);
        } else {
          // Token is invalid, clear local storage
          console.log('❌ [AUTH] Session expired or invalid, clearing auth data');

          // Show session expired notification if user was previously authenticated
          if (isAuthenticated) {
            notification.warning({
              message: 'Session Expired',
              description: 'Your session has expired. Please log in again.',
              duration: 5,
              placement: 'topRight'
            });
          }

          clearAuthData();
        }
      } else {
        console.log('🔍 [AUTH] No stored session found');
        clearAuthData();
      }
    } catch (error) {
      console.error('❌ [AUTH] Auth check failed:', error);
      clearAuthData();
    } finally {
      setLoading(false);
    }
  };

  const checkPackageAccess = async (userId) => {
    try {
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/users/${userId}/package-access`, {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setHasValidPackage(data.has_bot_insta_access);
      } else {
        setHasValidPackage(false);
      }
    } catch (error) {
      console.error('Package access check failed:', error);
      setHasValidPackage(false);
    }
  };

  const login = async (userData) => {
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('isAuthenticated', 'true');
    
    // Check package access after login
    await checkPackageAccess(userData.id);
  };

  const logout = async () => {
    try {
      console.log('🔍 [AUTH] Logging out user...');

      // Call logout endpoint to clear server-side session
      await fetch(`${AUTO_LOGIN_SERVICE_URL}/logout`, {
        method: 'POST',
        credentials: 'include',
      });

      console.log('✅ [AUTH] Server logout successful');
    } catch (error) {
      console.error('❌ [AUTH] Logout request failed:', error);
    } finally {
      clearAuthData();

      // Show logout notification
      notification.info({
        message: 'Logged Out',
        description: 'You have been successfully logged out.',
        duration: 3,
        placement: 'topRight'
      });
    }
  };

  const clearAuthData = () => {
    console.log('🔍 [AUTH] Clearing authentication data');
    setUser(null);
    setIsAuthenticated(false);
    setHasValidPackage(false);
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    hasValidPackage,
    login,
    logout,
    checkAuthStatus,
    checkPackageAccess: () => user ? checkPackageAccess(user.id) : Promise.resolve(),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
